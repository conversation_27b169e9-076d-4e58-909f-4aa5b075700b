import 'package:bottle_king_mobile/lib.dart';

class BottomNavScreen extends StatefulWidget {
  const BottomNavScreen({
    super.key,
    this.args,
  });

  final DashArg? args;

  @override
  State<BottomNavScreen> createState() => _BottomNavScreenState();
}

class _BottomNavScreenState extends State<BottomNavScreen> {
  int currentIndex = 0;

  List screens = [
    const HomeScreen(),
    const ShopScreen(),
    const OrderScreen(),
    const CartScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    if (widget.args?.index != null) {
      currentIndex = widget.args!.index!;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {}

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: BusyOverlay(
        show: false,
        child: Scaffold(
          body: screens[currentIndex],
          backgroundColor: AppColors.white,
          bottomNavigationBar: Container(
            height: Sizer.height(84),
            padding: EdgeInsets.only(
              bottom: Sizer.height(10),
            ),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                BottomNavColumn(
                  icon: AppSvgs.home,
                  isActive: currentIndex == 0,
                  labelText: 'Home',
                  onPressed: () {
                    currentIndex = 0;
                    setState(() {});
                  },
                ),
                BottomNavColumn(
                  icon: AppSvgs.shop,
                  isActive: currentIndex == 1,
                  labelText: 'Shop',
                  onPressed: () {
                    currentIndex = 1;
                    setState(() {});
                  },
                ),
                BottomNavColumn(
                  icon: AppSvgs.orders,
                  isActive: currentIndex == 2,
                  labelText: 'Orders',
                  onPressed: () {
                    currentIndex = 2;
                    setState(() {});
                  },
                ),
                BottomNavColumn(
                  icon: AppSvgs.file,
                  isActive: currentIndex == 3,
                  labelText: 'Cart',
                  onPressed: () {
                    currentIndex = 3;
                    setState(() {});
                  },
                ),
                BottomNavColumn(
                  icon: AppSvgs.user,
                  isActive: currentIndex == 4,
                  labelText: 'You',
                  onPressed: () {
                    currentIndex = 4;
                    setState(() {});
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
