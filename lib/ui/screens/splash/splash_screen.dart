import 'package:bottle_king_mobile/core/core.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    final authRef = ref.read(authVm);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LocationService.getUserLocationDetails(ref: ref);
    });

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.forward();

    Future.delayed(const Duration(seconds: 4), () async {
      final userLoaded = await authRef.loadUserFromStorage();
      // final hasSeenOnboarding = await StorageService.getBoolItem(
      //       StorageKey.hasSeenOnboarding,
      //     ) ??
      //     false;
      final nextPage = userLoaded
          ? RoutePath.bottomNavScreen
          // : hasSeenOnboarding
          //     ? RoutePath.bottomNavScreen
          : RoutePath.onboardingScreen;
      Navigator.of(context).pushReplacementNamed(nextPage);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black12,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          color: AppColors.black,
          child: Center(
            child: imageHelper(
              AppImages.logo,
              height: Sizer.height(120),
            ),
          ),
        ),
      ),
    );
  }
}
