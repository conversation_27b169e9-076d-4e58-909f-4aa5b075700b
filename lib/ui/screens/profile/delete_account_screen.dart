import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class DeleteAccountScreen extends ConsumerStatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  ConsumerState<DeleteAccountScreen> createState() =>
      _DeleteAccountScreenState();
}

class _DeleteAccountScreenState extends ConsumerState<DeleteAccountScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Delete account",
      ),
      body: ListView(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
        ),
        children: [
          const YBox(20),
          Text("When you delete your account, you will..",
              style: AppTypography.text18.copyWith(
                fontWeight: FontWeight.w500,
              )),
          const YBox(20),
          const DeleteAccountListTile(
            title: "Lose your order history",
            subtitle: "View order history",
          ),
          const YBox(24),
          const DeleteAccountListTile(
            title: "Lose your remaining loyalty points",
            subtitle: "View loyalty points",
          ),
          const YBox(24),
          const DeleteAccountListTile(
            title: "Lose your saved items",
            subtitle: "View saved items",
          ),
          const YBox(24),
          const DeleteAccountListTile(
            title: "Have to create a new account to undo \nthis action",
          ),
          const YBox(24),
          const YBox(140),
          CustomBtn.solid(
            onTap: () {
              // Navigator.pushNamed(context, RoutePath.otpScreen);
            },
            online: true,
            text: "Delete account",
          ),
          const YBox(24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                child: Text(
                  "Cancel",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class DeleteAccountListTile extends StatelessWidget {
  const DeleteAccountListTile({
    super.key,
    required this.title,
    this.subtitle,
  });

  final String title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: Sizer.height(20),
          width: Sizer.width(20),
          decoration: BoxDecoration(
            color: AppColors.grayD9,
            borderRadius: BorderRadius.circular(
              Sizer.radius(20),
            ),
          ),
        ),
        const XBox(16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (subtitle != null) const YBox(6),
              if (subtitle != null)
                Text(
                  subtitle ?? "",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
