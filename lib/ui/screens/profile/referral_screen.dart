import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class ReferralScreen extends ConsumerStatefulWidget {
  const ReferralScreen({super.key});

  @override
  ConsumerState<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends ConsumerState<ReferralScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Referral code",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(30),
            Container(
              width: Sizer.screenWidth,
              padding: EdgeInsets.all(Sizer.width(16)),
              decoration: const BoxDecoration(
                color: AppColors.black,
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        "Your referral link",
                        style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.white),
                      ),
                      const Spacer(),
                      InkWell(
                        onTap: () async {
                          await SharePlus.instance.share(ShareParams(
                            text:
                                "Hi! Enjoy 5% off your first order on bottleking using my \nreferral code '${authRef.user?.code ?? ""}'!, \nSign up and start ordering here: https://bottleking.ng/",
                            subject: "Referral code",
                            title: "Bottleking Referral code",
                          ));
                        },
                        child: Container(
                          padding: EdgeInsets.all(Sizer.radius(6)),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(20)),
                          ),
                          child: SvgPicture.asset(
                            AppSvgs.share,
                            height: Sizer.height(20),
                            width: Sizer.width(20),
                          ),
                        ),
                      )
                    ],
                  ),
                  const YBox(16),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: "Hi! Enjoy",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.white,
                            fontFamily: 'GeneralSans',
                            height: 1.5,
                          ),
                        ),
                        TextSpan(
                          text: " 5% ",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'GeneralSans',
                            height: 1.5,
                          ),
                        ),
                        TextSpan(
                          text:
                              "off your first order on bottleking using my referral code ",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.white,
                            fontFamily: 'GeneralSans',
                            height: 1.5,
                          ),
                        ),
                        TextSpan(
                          text: '"${authRef.user?.code ?? ""}"!',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'GeneralSans',
                            height: 1.5,
                          ),
                        ),
                        TextSpan(
                          text:
                              " Sign up and start ordering \nhere: https://bottleking.ng/",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.white,
                            fontFamily: 'GeneralSans',
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const YBox(24),
            Text(
              "Referral history",
              style: AppTypography.text18.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(16),
            Container(
              padding: EdgeInsets.all(Sizer.radius(16)),
              color: AppColors.greyF7,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Total points available",
                              style: AppTypography.text14.copyWith(
                                fontWeight: FontWeight.w500,
                                color: AppColors.gray75,
                              ),
                            ),
                            const YBox(12),
                            Text(
                              AppUtils.formatNumber(
                                decimalPlaces: 0,
                                number: authRef.user?.points ?? 0,
                              ),
                              style: AppTypography.text24.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Image.asset(
                        AppImages.coin,
                        height: Sizer.height(48),
                        width: Sizer.width(48),
                      ),
                    ],
                  ),
                  const YBox(20),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: "Redeem your reward point at checkout. ",
                          style: AppTypography.text14.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.gray75,
                            fontFamily: 'GeneralSans',
                          ),
                        ),
                        TextSpan(
                          text: "Learn more ",
                          style: AppTypography.text14.copyWith(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'GeneralSans',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const YBox(16),
            Row(
              children: [
                Expanded(
                  child: ReferralCard(
                    title: "Friends who ordered",
                    subtitle: AppUtils.formatNumber(
                      decimalPlaces: 0,
                      number: authRef.user?.friendsOrdered ?? 0,
                    ),
                    onTap: () {},
                  ),
                ),
                const XBox(16),
                Expanded(
                  child: ReferralCard(
                    title: "Friends who signed up",
                    subtitle: "20",
                    onTap: () {},
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ReferralCard extends StatelessWidget {
  const ReferralCard({
    super.key,
    required this.title,
    required this.subtitle,
    this.onTap,
  });

  final String title;
  final String subtitle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(Sizer.radius(16)),
        color: AppColors.greyF7,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.gray75,
              ),
            ),
            const YBox(12),
            Text(
              subtitle,
              style: AppTypography.text24.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
