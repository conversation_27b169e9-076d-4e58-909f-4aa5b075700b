import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final fullnameC = TextEditingController();
  final phoneC = TextEditingController();
  final emailC = TextEditingController();
  final birthdayC = TextEditingController();

  final fullNameFocus = FocusNode();
  final phoneFocus = FocusNode();
  final emailFocus = FocusNode();
  final birthdayFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initSet();
    });
  }

  _initSet() {
    final authRef = ref.watch(authVm);
    fullnameC.text = authRef.fullNames;
    phoneC.text = authRef.user?.phone ?? "";
    emailC.text = authRef.user?.email ?? "";
    //  birthdayC.text = authRef.user?.birthday ?? "";

    setState(() {});
  }

  @override
  void dispose() {
    fullnameC.dispose();
    phoneC.dispose();
    emailC.dispose();
    birthdayC.dispose();

    fullNameFocus.dispose();
    phoneFocus.dispose();
    emailFocus.dispose();
    birthdayFocus.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Edit profile",
      ),
      body: ListView(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
        ),
        children: [
          const YBox(24),
          CustomTextField(
            controller: fullnameC,
            focusNode: fullNameFocus,
            labelText: "Full name",
            showLabelHeader: true,
            borderRadius: 0,
          ),
          const YBox(20),
          CustomTextField(
            controller: phoneC,
            focusNode: phoneFocus,
            labelText: "Phone number",
            showLabelHeader: true,
            borderRadius: 0,
          ),
          const YBox(20),
          CustomTextField(
            controller: emailC,
            focusNode: emailFocus,
            labelText: "Email",
            showLabelHeader: true,
            borderRadius: 0,
          ),
          const YBox(20),
          CustomTextField(
            controller: birthdayC,
            focusNode: birthdayFocus,
            hintText: "Select month and day",
            labelText: "Birthday",
            showLabelHeader: true,
            isReadOnly: true,
            borderRadius: 0,
            suffixIcon: Icon(
              Iconsax.calendar_1,
              size: Sizer.height(24),
              color: AppColors.black70,
            ),
          ),
          const YBox(140),
          CustomBtn.solid(
            isOutline: true,
            textColor: AppColors.primaryBlack,
            onTap: () {
              // Navigator.pushNamed(context, RoutePath.otpScreen);
            },
            online: true,
            text: "Save",
          ),
          const YBox(24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.deleteAccountScreen);
                },
                child: Text(
                  "Delete account",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.red15,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
