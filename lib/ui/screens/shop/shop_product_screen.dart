import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ShopProductScreen extends ConsumerStatefulWidget {
  const ShopProductScreen({
    super.key,
    required this.category,
  });

  final String category;

  @override
  ConsumerState<ShopProductScreen> createState() => _ShopProductScreenState();
}

class _ShopProductScreenState extends ConsumerState<ShopProductScreen> {
  final List<Variation> _productsByCaterory = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    final r = await ref.read(productVm).getProductsByCategoryFilter(
          category: widget.category,
        );

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return CartFloatingActionButton(
      bottomPosition: 40,
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(80)),
          child: Container(
            // color: AppColors.red,
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Padding(
                        padding: EdgeInsets.all(Sizer.radius(4)),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: Sizer.height(20),
                          color: AppColors.black70,
                        ),
                      ),
                    ),
                    const XBox(8),
                    Expanded(
                      child: CustomTextField(
                        hintText: 'Find your favourite drinks',
                        fillColor: AppColors.grayF6,
                        hideBorder: true,
                        borderRadius: 0,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.all(6),
                          child: Container(
                            width: Sizer.width(40),
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(10),
                              vertical: Sizer.height(10),
                            ),
                            color: AppColors.primaryBlack,
                            child: SvgPicture.asset(AppSvgs.search),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const YBox(10),
              ],
            ),
          ),
        ),
        body: Column(
          children: [
            const YBox(10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Row(
                children: [
                  ShopFilter(
                    text: "Sort by",
                    onTap: () {},
                  ),
                  const XBox(12),
                  ShopFilter(
                    text: "Categories",
                    onTap: () {},
                  ),
                  const XBox(12),
                  ShopFilter(
                    text: "Price",
                    onTap: () {},
                  ),
                  const XBox(12),
                ],
              ),
            ),
            const YBox(6),
            Expanded(
              child: Builder(builder: (context) {
                if (productRef.busy(productsByCategoryFilter)) {
                  return GridView.count(
                    shrinkWrap: true,
                    // physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                      top: Sizer.height(10),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.68,
                    children: List.generate(
                      20,
                      (i) => Skeletonizer(
                        enabled: true,
                        child: HomeProductCard(
                          productVariation: Variation(
                            productName: "Glenfiddich 18yrs",
                            volume: "75cl",
                            unitPrice: 379500,
                            category: "BEST SELLER",
                          ),
                        ),
                      ),
                    ),
                  );
                }

                if (productRef.productsByCaterory.isEmpty) {
                  return Center(
                    child: Text("No products found for ${widget.category}"),
                  );
                }

                return GridView.count(
                  shrinkWrap: true,
                  // physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: Sizer.width(16),
                    right: Sizer.width(16),
                    top: Sizer.height(10),
                    bottom: Sizer.height(100),
                  ),
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  crossAxisCount: 2,
                  childAspectRatio: 0.64,
                  children: List.generate(
                    productRef.productsByCaterory.length,
                    (i) => HomeProductCard(
                      productVariation: productRef.productsByCaterory[i],
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.productDetailsScreen,
                          arguments: productRef.productsByCaterory[i],
                        );
                      },
                    ),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
