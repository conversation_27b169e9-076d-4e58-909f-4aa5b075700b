import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final List<Variation> _productsByCaterory = [];

  // OrderDeliveryType _orderDeliveryType = OrderDeliveryType.delivery;
  int _categoryIndex = -1;
  String? _selectedCategory;
  String _selectedExtraCategory = "bestsellers";

  final Map<String, String> _extraCategoryMap = {
    "Best sellers": "bestsellers",
    "Recommended": "recommended",
    "New arrivals": "newarrival",
    "Discount": "discount",
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initSetup();
    });
  }

  _initSetup() async {
    final prodRef = ref.read(productVm);
    ref.read(addressVm.notifier).getAddresses();
    prodRef.getProductCategories();

    getProductsByCategory();
  }

  getProductsByCategory({
    String? category,
    String? extraCategory,
  }) async {
    final r = await ref.read(productVm).getProductsByCategoryFilter(
          category: category,
          extraCategory: extraCategory ?? _selectedExtraCategory,
        );

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    final productRef = ref.watch(productVm);
    final addressRef = ref.watch(addressVm);
    return CartFloatingActionButton(
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(60)),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
              vertical: Sizer.height(8),
            ),
            color: AppColors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: const AddressModal(),
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  '${addressRef.orderDeliveryType.title} location',
                                  style: AppTypography.text14.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.black70,
                                  ),
                                ),
                                // const XBox(4),
                                // if (ref.watch(scheduleVmodel).isSchedule)
                                //   Text(
                                //     "${ref.watch(scheduleVmodel).formatedScheduleDate} at ${ref.watch(scheduleVmodel).scheduleTimeString}",
                                //     style: AppTypography.text14.copyWith(
                                //       fontWeight: FontWeight.w500,
                                //       color: AppColors.black70,
                                //     ),
                                //   ),
                              ],
                            ),
                            const YBox(2),
                            Text(
                              addressRef.address,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTypography.text14.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    const XBox(8),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      size: 20,
                    ),
                    const XBox(8),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, RoutePath.referralScreen);
                      },
                      child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(8),
                            vertical: Sizer.height(9),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.greyF7,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(50)),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                AppImages.gift,
                                height: Sizer.height(20),
                              ),
                              const XBox(4),
                              Text(
                                AppUtils.formatNumber(
                                  decimalPlaces: 0,
                                  number: authRef.user?.points ?? 0,
                                ),
                                style: AppTypography.text14.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          )),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        body: ListView(
          children: [
            const YBox(20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Row(children: [
                HomeTab(
                  text: "Delivery",
                  isSelected: addressRef.orderDeliveryType ==
                      OrderDeliveryType.delivery,
                  margin: EdgeInsets.only(
                    right: Sizer.width(16),
                  ),
                  onTap: () {
                    ref
                        .read(addressVm.notifier)
                        .setOrderDeliveryType(OrderDeliveryType.delivery);
                  },
                ),
                HomeTab(
                  text: "Pick-up",
                  isSelected:
                      addressRef.orderDeliveryType == OrderDeliveryType.pickup,
                  margin: EdgeInsets.only(
                    right: Sizer.width(16),
                  ),
                  onTap: () {
                    ref
                        .read(addressVm)
                        .setOrderDeliveryType(OrderDeliveryType.pickup);
                  },
                ),
                HomeTab(
                  text: "Schedule",
                  margin: EdgeInsets.only(
                    right: Sizer.width(16),
                  ),
                  isSelected: ref.watch(scheduleVmodel).isSchedule,
                  onTap: () {
                    ModalWrapper.bottomSheet(
                      context: context,
                      widget: const ScheduleOrderModal(),
                    );
                  },
                ),
                HomeTab(
                  text: "Group Order",
                  margin: EdgeInsets.only(
                    right: Sizer.width(16),
                  ),
                  onTap: () {},
                ),
              ]

                  // List.generate(
                  //   4,
                  //   (i) => HomeTab(
                  //     text: [
                  //       "Delivery",
                  //       "Pick-up",
                  //       "Schedule",
                  //       "Group Order",
                  //     ][i],
                  //     isSelected: _orderDeliveryType == DeliveryType.values[i],
                  //     margin: EdgeInsets.only(
                  //       right: Sizer.width(16),
                  //     ),
                  //     onTap: () {

                  //       setState(() {});
                  //       if (_orderDeliveryType == DeliveryType.schedule) {
                  //         ModalWrapper.bottomSheet(
                  //           context: context,
                  //           widget: const ScheduleOrderModal(),
                  //         );
                  //       }
                  //     },
                  //   ),
                  // ),
                  ),
            ),
            const YBox(20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: CustomTextField(
                hintText: 'Find your favourite drinks',
                fillColor: AppColors.grayF6,
                hideBorder: true,
                isReadOnly: true,
                borderRadius: 0,
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(6),
                  child: Container(
                    width: Sizer.width(40),
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(10),
                      vertical: Sizer.height(10),
                    ),
                    color: AppColors.primaryBlack,
                    child: SvgPicture.asset(AppSvgs.search),
                  ),
                ),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.searchProductScreen,
                  );
                },
              ),
            ),
            const YBox(16),
            Builder(builder: (ctx) {
              if (productRef.isBusy) {
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(children: [
                    const XBox(16),
                    ...List.generate(
                      10,
                      (i) {
                        return Padding(
                          padding: EdgeInsets.only(
                            right: Sizer.width(16),
                          ),
                          child: Skeletonizer(
                            enabled: true,
                            child: Bone(
                              height: Sizer.height(18),
                              width: Sizer.width(70),
                              borderRadius: BorderRadius.circular(
                                Sizer.radius(8),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ]),
                );
              }
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(children: [
                  const XBox(16),
                  HomeTabOutline(
                    text: 'All',
                    isSelected: _categoryIndex == -1,
                    onTap: () async {
                      _categoryIndex = -1;
                      _selectedCategory = null;
                      setState(() {});
                      getProductsByCategory(
                        extraCategory: _selectedExtraCategory,
                      );
                    },
                  ),
                  ...List.generate(
                    productRef.productCategories.length,
                    (i) {
                      final c = productRef.productCategories[i];
                      return HomeTabOutline(
                        text: productCategoryHelper(c.category ?? ''),
                        isSelected: _categoryIndex == i,
                        onTap: () {
                          _categoryIndex = i;
                          _selectedCategory = c.category;
                          setState(() {});
                          getProductsByCategory(
                            category: c.category,
                            extraCategory: _selectedExtraCategory,
                          );
                        },
                      );
                    },
                  ),
                ]),
              );
            }),
            const YBox(12),
            const HomeSlider(),
            const YBox(20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(children: [
                ..._extraCategoryMap.entries.map((entry) {
                  return HomeTabOutline(
                    text: entry.key,
                    isSelected: _selectedExtraCategory == entry.value,
                    onTap: () {
                      _selectedExtraCategory = entry.value;
                      setState(() {});
                      getProductsByCategory(
                        category: _selectedCategory,
                        extraCategory: entry.value,
                      );
                    },
                  );
                }),
              ]),
            ),
            const YBox(20),
            Builder(builder: (context) {
              if (productRef.busy(productsByCategoryFilter)) {
                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: Sizer.width(16),
                    right: Sizer.width(16),
                    bottom: Sizer.height(100),
                  ),
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  crossAxisCount: 2,
                  childAspectRatio: 0.68,
                  children: List.generate(
                    20,
                    (i) => Skeletonizer(
                      enabled: true,
                      child: HomeProductCard(
                        productVariation: Variation(
                          productName: "Glenfiddich 18yrs",
                          volume: "75cl",
                          unitPrice: 379500,
                          category: "BEST SELLER",
                        ),
                      ),
                    ),
                  ),
                );
              }
              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                  right: Sizer.width(16),
                  bottom: Sizer.height(100),
                ),
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                crossAxisCount: 2,
                childAspectRatio: 0.64,
                children: List.generate(
                  _productsByCaterory.length,
                  (i) => HomeProductCard(
                    productVariation: _productsByCaterory[i],
                    onTap: () {
                      Navigator.pushNamed(
                        context,
                        RoutePath.productDetailsScreen,
                        arguments: _productsByCaterory[i],
                      );
                    },
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
