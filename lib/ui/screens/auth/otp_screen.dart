import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class OtpScreen extends StatelessWidget {
  const OtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "",
      ),
      body: ListView(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
        ),
        children: [
          imageHelper(
            AppImages.logoBlack,
            height: Sizer.height(48),
          ),
          const YBox(30),
          Text(
            "Verify your number",
            textAlign: TextAlign.center,
            style: AppTypography.text24.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(4),
          Text(
            "We’ve sent a 4- digit code to 09000000000 via \nSMS and Whatsapp",
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.black70,
            ),
          ),
          const YBox(40),
          Text(
            "Enter OTP",
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(6),
          Pinput(
            defaultPinTheme: PinInputTheme.defaultPinTheme(),
            followingPinTheme: PinInputTheme.followPinTheme(),
            length: 4,
            // controller: vm.emailOtp,
            // focusNode: pinFocusNode,
            showCursor: true,
            // onChanged: (value) => vm..reBuildUI(),
            onCompleted: (pin) {
              // _verifyOtp();
            },
          ),
          // const YBox(24),
          // ResendCode(
          //   onResendCode: () {
          //     // vm.requestOtp(otpType: OtpType.email);
          //   },
          // ),
          // const YBox(24),

          const YBox(20),
          Text(
            "Tap here to resend OTP",
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w600,
            ),
          )
        ],
      ),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
          top: Sizer.height(5),
          bottom: Sizer.height(30),
        ),
        child: CustomBtn.solid(
          onTap: () {
            Navigator.pushNamed(context, RoutePath.bottomNavScreen);
          },
          online: true,
          text: "Verify",
        ),
      ),
    );
  }
}
