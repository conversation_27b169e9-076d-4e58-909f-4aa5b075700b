import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/gestures.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final emailC = TextEditingController(text: "<EMAIL>");
  final passwordC = TextEditingController(text: "Test@123");
  final emailF = FocusNode();
  final passwordF = FocusNode();

  @override
  void dispose() {
    emailC.dispose();
    passwordC.dispose();
    emailF.dispose();
    passwordF.dispose();

    super.dispose();
  }

  bool get isEmailValid =>
      emailC.text.trim().contains('@') && emailC.text.trim().contains('.');
  bool get formValid => isEmailValid && passwordC.text.trim().isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            imageHelper(
              AppImages.logoBlack,
              height: Sizer.height(48),
            ),
            const YBox(30),
            Text(
              "Welcome back",
              textAlign: TextAlign.center,
              style: AppTypography.text24.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(4),
            Text(
              "Login to your account to continue shopping your \nfavourite drinks",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
              ),
            ),
            const YBox(40),
            CustomTextField(
              controller: emailC,
              focusNode: emailF,
              labelText: "Email",
              showLabelHeader: true,
              borderRadius: 0,
              errorText: emailF.hasFocus && !isEmailValid
                  ? 'Enter a valid email'
                  : null,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              controller: passwordC,
              focusNode: passwordF,
              labelText: "Password",
              showLabelHeader: true,
              borderRadius: 0,
              isPassword: true,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "New user? sign in with",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " phone number",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pop(context);
                      },
                  ),
                ],
              ),
            ),
            const YBox(24),
            CustomBtn.solid(
              onTap: () async {
                FocusManager.instance.primaryFocus?.unfocus();
                final r = await ref.read(authVm.notifier).login(
                        args: AuthArg(
                      username: emailC.text.trim(),
                      password: passwordC.text.trim(),
                    ));
                handleApiResponse(
                  response: r,
                  successMsg: "Welcome back to the party",
                  onSuccess: () {
                    Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                  },
                );
              },
              online: formValid,
              text: "Continue",
            ),
            const YBox(24),
            Text(
              "OR",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(24),
            // SocialBtn(
            //   iconPath: AppSvgs.google,
            //   btnText: "Sign up with Google",
            //   onTap: () {},
            // ),
            // const YBox(12),
            // SocialBtn(
            //   iconPath: AppSvgs.apple,
            //   btnText: "Sign up with Apple",
            //   onTap: () {},
            // ),
            const YBox(20),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Don’t have an account? ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " Sign up",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pushNamed(context, RoutePath.registerScreen);
                      },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
