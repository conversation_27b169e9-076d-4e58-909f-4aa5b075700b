import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/gestures.dart';

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _fullNameC = TextEditingController();
  final _emailC = TextEditingController();
  final _phoneC = TextEditingController();
  final _dobC = TextEditingController();

  final _fullNameF = FocusNode();
  final _emailF = FocusNode();
  final _phoneF = FocusNode();
  final _dobF = FocusNode();

  DateTime? _selectedDOB;

  @override
  void dispose() {
    _fullNameC.dispose();
    _emailC.dispose();
    _phoneC.dispose();
    _dobC.dispose();

    _fullNameF.dispose();
    _emailF.dispose();
    _phoneF.dispose();
    _dobF.dispose();

    super.dispose();
  }

  bool get btnIsActive {
    return _fullNameC.text.isNotEmpty &&
        _emailC.text.isNotEmpty &&
        _phoneC.text.isNotEmpty &&
        _selectedDOB != null;
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            imageHelper(
              AppImages.logoBlack,
              height: Sizer.height(48),
            ),
            const YBox(24),
            CustomTextField(
              controller: _fullNameC,
              focusNode: _fullNameF,
              labelText: "Full name",
              showLabelHeader: true,
              borderRadius: 0,
            ),
            const YBox(16),
            CustomTextField(
              controller: _emailC,
              focusNode: _emailF,
              labelText: "Email",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              controller: _phoneC,
              focusNode: _phoneF,
              hintText: "enter phone number",
              labelText: "Phone number",
              showLabelHeader: true,
              borderRadius: 0,
              keyboardType: KeyboardType.phone,
              onChanged: (p0) => setState(() {}),
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(10)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "NG",
                      style: AppTypography.text16.copyWith(),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: Sizer.height(24),
                      color: AppColors.black70,
                    )
                  ],
                ),
              ),
            ),
            const YBox(16),
            CustomTextField(
              controller: _dobC,
              focusNode: _dobF,
              hintText: "Select date",
              labelText: "Birthday",
              showLabelHeader: true,
              isReadOnly: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
              suffixIcon: Icon(
                Iconsax.calendar_1,
                size: Sizer.height(24),
                color: AppColors.black70,
              ),
              onTap: () {
                CustomCupertinoDatePicker(
                  context: context,
                  onDateTimeChanged: (dateTime) {
                    _selectedDOB = dateTime;
                    _dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                  },
                ).show();
              },
            ),
            const YBox(24),
            CustomBtn.solid(
              onTap: () async {
                // Navigator.pushNamed(context, RoutePath.otpScreen);
                FocusScope.of(context).unfocus();
                final r = await ref.read(authVm.notifier).createAccount(
                      firstName: _fullNameC.text,
                      lastName: _fullNameC.text,
                      email: _emailC.text,
                      phone: _phoneC.text,
                      dob: _selectedDOB?.toIso8601String() ?? "",
                    );

                handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pushNamed(context, RoutePath.otpScreen);
                  },
                );
              },
              online: btnIsActive,
              text: "Continue",
            ),
            // const YBox(24),
            // Text(
            //   "OR",
            //   textAlign: TextAlign.center,
            //   style: AppTypography.text14.copyWith(
            //     color: AppColors.black70,
            //     fontWeight: FontWeight.w600,
            //   ),
            // ),
            // const YBox(24),
            // SocialBtn(
            //   iconPath: AppSvgs.google,
            //   btnText: "Sign up with Google",
            //   onTap: () {},
            // ),
            // const YBox(12),
            // SocialBtn(
            //   iconPath: AppSvgs.apple,
            //   btnText: "Sign up with Apple",
            //   onTap: () {},
            // ),
            const YBox(20),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Already have an account? ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " Login",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pop(context);
                      },
                  ),
                ],
              ),
            ),
            const YBox(140),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "By continuing, you agree to our ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: "Terms of Use",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " and acknowlegde that you have read our ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: "Privacy Policy",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
