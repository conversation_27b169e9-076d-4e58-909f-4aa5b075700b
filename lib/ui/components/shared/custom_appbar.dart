import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({
    super.key,
    required this.title,
    this.trailingIcon,
    this.onTrailingIconTap,
    this.onBack,
  });

  final String title;
  final String? trailingIcon;
  final VoidCallback? onTrailingIconTap;
  final VoidCallback? onBack;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(60)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    } else if (onBack != null) {
                      onBack!();
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: Sizer.height(16),
                      color: AppColors.black70,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: Sizer.height(20)),
                  child: Text(
                    title,
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (trailingIcon != null)
                  Container(
                    padding: EdgeInsets.all(15.0.r),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.grey,
                        width: 1.5,
                      ),
                    ),
                    child: InkWell(
                      onTap: onTrailingIconTap,
                      child: SvgPicture.asset(trailingIcon!),
                    ),
                  )
                else
                  Container(width: Sizer.width(24)),
              ],
            ),
          ),
          const YBox(6)
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(Sizer.height(60));
}
