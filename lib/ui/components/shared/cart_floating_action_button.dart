import 'package:bottle_king_mobile/lib.dart';

class CartFloatingActionButton extends ConsumerStatefulWidget {
  const CartFloatingActionButton({
    super.key,
    required this.child,
    this.bottomPosition,
  });

  final Widget child;
  final double? bottomPosition;

  @override
  ConsumerState<CartFloatingActionButton> createState() =>
      _CartFloatingActionButtonState();
}

class _CartFloatingActionButtonState
    extends ConsumerState<CartFloatingActionButton> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartVm.notifier).getCart(showLoader: false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Stack(
        children: [
          SizedBox(
            width: Sizer.screenWidth,
            height: Sizer.screenHeight,
            child: widget.child,
          ),
          Positioned(
            bottom: widget.bottomPosition ?? 20,
            right: 0,
            left: 0,
            child: Consumer(
              builder: (context, ref, child) {
                final cartRef = ref.watch(cartVm);
                return cartRef.cartModel?.items?.isNotEmpty == true
                    ? Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                        ),
                        child: CustomBtn.withChild(
                            onlineColor: AppColors.primaryBlack,
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.bottomNavScreen,
                                arguments: DashArg(index: 3),
                              );
                            },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: Sizer.width(16),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Proceed to order ${cartRef.cartModel?.items?.length ?? 0} item",
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    "NGN ${AppUtils.formatNumber(decimalPlaces: 0, number: cartRef.cartModel?.total ?? 0)}",
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            )),
                      )
                    : const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}
