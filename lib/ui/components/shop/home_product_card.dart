import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class HomeProductCard extends ConsumerStatefulWidget {
  const HomeProductCard({
    super.key,
    this.onTap,
    this.fromCart = false,
    required this.productVariation,
  });

  final Variation productVariation;
  final bool fromCart;
  final Function()? onTap;

  @override
  ConsumerState<HomeProductCard> createState() => _HomeProductCardState();
}

class _HomeProductCardState extends ConsumerState<HomeProductCard> {
  bool isFavorite = false;
  @override
  Widget build(BuildContext context) {
    // final cartRef = ref.watch(cartVm);
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        color: AppColors.white,
        height: Sizer.height(220),
        width: Sizer.height(170),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                Container(
                  padding: EdgeInsets.all(Sizer.radius(26)),
                  height: Sizer.height(165),
                  width: Sizer.screenWidth,
                  decoration: const BoxDecoration(
                    color: AppColors.greyF7,
                  ),
                  child: MyCachedNetworkImage(
                    imageUrl: widget.productVariation.image ?? "",
                  ),
                ),
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Container(
                    color: AppColors.white,
                    padding: EdgeInsets.all(Sizer.radius(2)),
                    child: LikeButton(
                      isFavorite: isFavorite,
                      iconColor: AppColors.primaryBlack,
                      splashColor: AppColors.primaryBlack,
                      onChanged: (value) async {
                        isFavorite = value;
                        setState(() {});
                        final cRef = ref.read(cartVm);
                        await cRef.addToCartOrWishlist(
                          productId: widget.productVariation.productId ?? "",
                          variationId: widget.productVariation.id ?? '',
                          isWishList: true,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            const YBox(8),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productCategoryHelper(
                            widget.productVariation.category ?? "")
                        .toUpperCase(),
                    style: AppTypography.text10.copyWith(
                      color: AppColors.yellow37,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YBox(2),
                  Text(
                    widget.productVariation.name ?? "",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(2),
                  Text(
                    widget.productVariation.volume ?? "",
                    style: AppTypography.text12.copyWith(
                      color: AppColors.black70,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(2),
                  Row(
                    children: [
                      Text(
                        AppUtils.formatNumber(
                          decimalPlaces: 0,
                          number: widget.productVariation.unitPrice ?? 0,
                        ),
                        style: AppTypography.text16.copyWith(
                          color: AppColors.primaryBlack,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Skeleton.replace(
                        replacement: const Bone.icon(size: 30),
                        child: InkWell(
                          onTap: () {
                            ModalWrapper.bottomSheet(
                              context: context,
                              widget: CartPopupModal(
                                productVariation: widget.productVariation,
                                fromCart: widget.fromCart,
                              ),
                            );
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: (Sizer.width(16)),
                              vertical: (Sizer.radius(8)),
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                width: 1,
                                color: AppColors.blackBD,
                              ),
                              borderRadius:
                                  BorderRadius.circular(Sizer.radius(50)),
                            ),
                            child: SvgPicture.asset(AppSvgs.cart),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
