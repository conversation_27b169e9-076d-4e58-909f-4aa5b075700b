import 'package:bottle_king_mobile/core/core.dart';

class ShopCategoryCard extends StatelessWidget {
  const ShopCategoryCard({
    super.key,
    required this.image,
    required this.title,
    this.onTap,
  });

  final String image;
  final String title;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        children: [
          SizedBox(
            height: Sizer.height(123),
            width: double.infinity,
            child: MyCachedNetworkImage(
              imageUrl: image,
              fit: BoxFit.contain,
            ),
          ),
          Container(
            height: Sizer.height(123),
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.5),
                  Colors.black.withOpacity(0.1),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 10,
            left: 10,
            child: Text(
              title,
              style: AppTypography.text16.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
