import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ExtraCategoryProduct extends ConsumerStatefulWidget {
  const ExtraCategoryProduct({
    super.key,
    required this.category,
  });

  final String category;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ExtraCategoryProductState();
}

class _ExtraCategoryProductState extends ConsumerState<ExtraCategoryProduct> {
  final List<Variation> _productsByCaterory = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // getExtraCategoryProducts();
    });
  }

  getExtraCategoryProducts() async {
    final r = await ref
        .read(productVm)
        .getProductsByCategoryFilter(category: widget.category);

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(16),
            vertical: Sizer.height(10),
          ),
          child: Text(
            "Related products",
            style: AppTypography.text18.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Builder(builder: (context) {
          if (productRef.busy(productsByCategoryFilter)) {
            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.height(100),
              ),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              crossAxisCount: 2,
              childAspectRatio: 0.68,
              children: List.generate(
                20,
                (i) => Skeletonizer(
                  enabled: true,
                  child: HomeProductCard(
                    productVariation: Variation(
                      productName: "Glenfiddich 18yrs",
                      volume: "75cl",
                      unitPrice: 379500,
                      category: "BEST SELLER",
                    ),
                  ),
                ),
              ),
            );
          }
          final products = _productsByCaterory.take(6).toList();
          return GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              bottom: Sizer.height(100),
            ),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            crossAxisCount: 2,
            childAspectRatio: 0.64,
            children: List.generate(
              products.length,
              (i) => HomeProductCard(
                productVariation: products[i],
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.productDetailsScreen,
                    arguments: products[i],
                  );
                },
              ),
            ),
          );
        }),
      ],
    );
  }
}
