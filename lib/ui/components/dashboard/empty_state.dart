import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class EmptyState extends StatelessWidget {
  const EmptyState({
    super.key,
    required this.text,
    required this.btnText,
    required this.onTap,
  });

  final String text;
  final String btnText;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.w500),
            ),
            const YBox(16),
            CustomBtn.solid(
              onTap: onTap,
              text: btnText,
            ),
          ],
        ),
      ),
    );
  }
}
