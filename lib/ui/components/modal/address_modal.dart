import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AddressModal extends ConsumerStatefulWidget {
  const AddressModal({super.key});

  @override
  ConsumerState<AddressModal> createState() => _AddressModalState();
}

class _AddressModalState extends ConsumerState<AddressModal>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  // Track the height for animation
  double get _containerHeight =>
      selectedIndex == 0 ? Sizer.screenHeight * 0.8 : Sizer.screenHeight * 0.45;

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 2, vsync: this);

    // Set initial tab based on orderDeliveryType
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check the current orderDeliveryType and set the appropriate tab
      if (ref.read(addressVm).isPickup) {
        selectedIndex = 1;
        _tabController.animateTo(1);
      } else {
        selectedIndex = 0;
        _tabController.animateTo(0);
      }
      setState(() {});
    });

    // Listen to tab changes
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      setState(() {
        selectedIndex = _tabController.index;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: _containerHeight,
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: SvgPicture.asset(AppSvgs.close),
            ),
          ),
          const YBox(16),
          Container(
            height: Sizer.height(42),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.greyF7,
              // borderRadius: BorderRadius.circular(Sizer.radius(12)),
              border: Border.all(width: 1, color: AppColors.grayF0),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(6),
              vertical: Sizer.height(4),
            ),
            child: TabBar(
              splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
              physics: const NeverScrollableScrollPhysics(),
              onTap: (int value) {
                setState(() {
                  selectedIndex = value;
                });
              },
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
                color: AppColors.white,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: AppColors.primaryBlack,
              automaticIndicatorColorAdjustment: true,
              labelStyle: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w500,
              ),
              controller: _tabController,
              tabs: const [
                Tab(text: 'Delivery'),
                Tab(text: 'Pick - up'),
              ],
            ),
          ),
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 400),
              child: selectedIndex == 0
                  ? const AddressDeliveryTab(key: ValueKey('delivery'))
                  : const AddressPickupTab(key: ValueKey('pickup')),
            ),
          ),
        ],
      ),
    );
  }
}
