import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CartPopupModal extends ConsumerStatefulWidget {
  const CartPopupModal({
    super.key,
    required this.productVariation,
    this.fromCart = false,
  });

  final Variation productVariation;
  final bool fromCart;

  @override
  ConsumerState<CartPopupModal> createState() => _CartPopupModalState();
}

class _CartPopupModalState extends ConsumerState<CartPopupModal> {
  final qtyController = TextEditingController();
  int _productQty = 1;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      qtyController.text = _productQty.toString();
      setState(() {});
    });
  }

  @override
  void dispose() {
    qtyController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cartRef = ref.watch(cartVm);
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Add item to cart',
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          CartProductCard(
            cartItem: CartItem(
              variationName: widget.productVariation.category ?? "",
              productName: widget.productVariation.productName ?? "",
              image: widget.productVariation.image ?? "",
              cartonSize: widget.productVariation.volume ?? "",
              price: widget.productVariation.unitPrice ?? 0,
              productId: widget.productVariation.productId ?? "",
              variationId: widget.productVariation.id ?? "",
              // quantity: _productQty,
            ),
          ),
          const YBox(16),
          Row(
            children: [
              Text(
                'Qty: ',
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const XBox(10),
              CartIncrement(
                controller: qtyController,
                value: _productQty,
                onCrement: () {
                  _productQty++;
                  qtyController.text = _productQty.toString();
                  setState(() {});
                },
                onDecrement: () {
                  if (_productQty > 1) {
                    _productQty--;
                    qtyController.text = _productQty.toString();
                    setState(() {});
                  }
                },
              )
            ],
          ),
          const YBox(30),
          cartRef.isBusy
              ? const BtnLoadState()
              : Column(
                  children: [
                    CustomBtn.solid(
                      isLoading: cartRef.isBusy,
                      onTap: () async {
                        final r = await _addProductToCart();

                        handleApiResponse(
                          response: r,
                          onSuccess: () {
                            Navigator.pop(context);
                            if (widget.fromCart) {
                              ref.read(cartVm).getCart(showLoader: false);
                            }
                          },
                        );
                      },
                      online: true,
                      text: "Add to cart",
                    ),
                    const YBox(10),
                    CustomBtn.solid(
                      onTap: () async {
                        final r = await _addProductToCart();

                        handleApiResponse(
                          response: r,
                          onSuccess: () async {
                            // Navigator.pop(context);
                            await ref.read(cartVm).getCart(showLoader: false);
                            Navigator.pushNamed(
                              context,
                              RoutePath.checkoutScreen,
                              arguments: ref.read(cartVm).cartModel,
                            );
                          },
                        );
                      },
                      isLoading: cartRef.isBusy,
                      online: true,
                      isOutline: true,
                      outlineColor: AppColors.primaryBlack,
                      textColor: AppColors.primaryBlack,
                      text: "Buy now",
                    ),
                  ],
                ),
          const YBox(40),
        ],
      ),
    );
  }

  Future<ApiResponse> _addProductToCart() async {
    printty("cart popup productId ${widget.productVariation.id}");
    final cRef = ref.read(cartVm);
    final r = await cRef.addToCartOrWishlist(
      productId: widget.productVariation.productId ?? '',
      variationId: widget.productVariation.id ?? '',
      quantity: _productQty.toString(),
    );
    if (r.success) {
      return r;
    }

    return ApiResponse(
      success: false,
      message: "Something went wrong",
    );
  }
}
