import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AddressDeliveryTab extends ConsumerStatefulWidget {
  const AddressDeliveryTab({
    super.key,
  });

  @override
  ConsumerState<AddressDeliveryTab> createState() => _AddressDeliveryTabState();
}

class _AddressDeliveryTabState extends ConsumerState<AddressDeliveryTab> {
  AddressArg? _selectedAddress;
  int _selectedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final addRef = ref.watch(addressVm);
    return Column(
      children: [
        const YBox(24),
        InkWell(
          onTap: () {
            Navigator.pushNamed(context, RoutePath.addAddressScreen);
          },
          child: Row(
            children: [
              Icon(
                Icons.add,
                color: AppColors.black70,
                size: Sizer.height(20),
              ),
              const XBox(10),
              Text(
                'Use new address',
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        const YBox(16),
        const Divider(thickness: 1, color: AppColors.grayE6),
        const YBox(15),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.location_on_outlined,
              color: AppColors.black70,
            ),
            const XBox(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current location',
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.black70,
                    ),
                  ),
                  const YBox(10),
                  Text(
                    addRef.currentAddress?.fullAddress ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const YBox(10),
        const Divider(thickness: 1, color: AppColors.grayE6),
        const YBox(16),

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(
              AppSvgs.home,
              height: Sizer.height(20),
              width: Sizer.height(20),
            ),
            const XBox(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Home',
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(10),
                  Text(
                    addRef.currentAddress?.fullAddress ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, RoutePath.addAddressScreen);
              },
              child: Text(
                'Add address',
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.black70,
                ),
              ),
            ),
          ],
        ),
        const YBox(16),
        Expanded(
          child: LoadableContentBuilder(
              isBusy: addRef.busy(getAddressState),
              items: addRef.addresses,
              loadingBuilder: (context) {
                return const SizerLoader();
              },
              emptyBuilder: (context) {
                return Center(
                  child: Text(
                    "No address found",
                    style: AppTypography.text18.copyWith(
                        fontWeight: FontWeight.w500, color: AppColors.black70),
                  ),
                );
              },
              contentBuilder: (context) {
                final adds = addRef.addresses.take(4).toList();
                return RefreshIndicator(
                  onRefresh: () async {
                    addRef.getAddresses();
                  },
                  child: ListView.separated(
                    padding: EdgeInsets.only(
                      top: Sizer.height(20),
                      bottom: Sizer.height(100),
                    ),
                    itemBuilder: (ctx, i) {
                      final a = adds[i];
                      return InkWell(
                        onTap: () {
                          _selectedIndex = i;
                          _selectedAddress = AddressArg(
                              fullAddress: a.fullAddress,
                              lat: a.lat,
                              lng: a.lng);
                          setState(() {});
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: Sizer.height(10),
                          ),
                          decoration: BoxDecoration(
                            color: _selectedIndex == i
                                ? AppColors.greyF7
                                : Colors.transparent,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                color: AppColors.black70,
                                size: Sizer.radius(16),
                              ),
                              const XBox(10),
                              Expanded(
                                child: Text(
                                  a.fullAddress ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppTypography.text14.copyWith(),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (_, __) => const YBox(10),
                    itemCount: adds.length,
                  ),
                );
              }),
        ),
        // const Divider(thickness: 1, color: AppColors.grayE6),
        // const YBox(30),
        CustomBtn.solid(
          onTap: () {
            if (_selectedAddress != null) {
              ref.read(addressVm)
                ..setDeliveryAddress(_selectedAddress!)
                ..setOrderDeliveryType(OrderDeliveryType.delivery);
              Navigator.pop(context, OrderDeliveryType.delivery);
            }
          },
          online: _selectedAddress != null,
          text: "Confirm",
        ),
        const YBox(30),
      ],
    );
  }
}
