import 'package:bottle_king_mobile/core/core.dart';

import '../../components.dart';

class OngoingTab extends ConsumerStatefulWidget {
  const OngoingTab({
    super.key,
  });

  @override
  ConsumerState<OngoingTab> createState() => _OngoingTabState();
}

class _OngoingTabState extends ConsumerState<OngoingTab> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderViewModel.notifier).getOrdersList(status: "pending");
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderRef = ref.watch(orderViewModel);
    return LoadableContentBuilder(
      isBusy: orderRef.isBusy,
      items: orderRef.orderList,
      loadingBuilder: (context) {
        return const SizerLoader(height: 600);
      },
      emptyBuilder: (context) {
        return EmptyState(
          text: " No orders found",
          btnText: "Start shopping",
          onTap: () {
            Navigator.pushNamed(
              context,
              RoutePath.bottomNavScreen,
              arguments: DashArg(index: 1),
            );
          },
        );
      },
      contentBuilder: (context) {
        return ListView.separated(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            top: Sizer.width(16),
            bottom: Sizer.width(100),
          ),
          itemBuilder: (ctx, i) {
            final o = orderRef.orderList[i];
            return OrderProductCard(order: o);
          },
          separatorBuilder: (_, __) => Padding(
            padding: EdgeInsets.symmetric(
              vertical: Sizer.height(6),
            ),
            child: const Divider(color: AppColors.grayE6),
          ),
          itemCount: orderRef.orderList.length,
        );
      },
    );
  }
}
