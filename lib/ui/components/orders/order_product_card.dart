import 'package:bottle_king_mobile/core/core.dart';

class OrderProductCard extends StatelessWidget {
  const OrderProductCard({
    super.key,
    required this.order,
  });

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          RoutePath.orderDetailsScreen,
          arguments: order,
        );
      },
      child: Row(
        children: [
          Stack(
            children: [
              SizedBox(
                width: Sizer.width(90),
                height: Sizer.height(90),
                child: MyCachedNetworkImage(
                  imageUrl: order.products?.isNotEmpty == true
                      ? order.products?.first.image ?? ""
                      : "",
                ),
              ),
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.45),
                        Colors.black.withValues(alpha: 0.25),
                      ],
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '${order.products?.length} ${order.products?.length == 1 ? 'Item' : 'Items'}',
                    style: AppTypography.text16.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const XBox(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Id: ${order.orderId}',
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(4),
                Text(
                  AppUtils.getDeliveryStatus(
                    s: order.status ?? "",
                    dDate: order.deliveryDate,
                    dTime: order.deliveryTime,
                  ),
                  style: AppTypography.text12.copyWith(
                    color: AppColors.black70,
                  ),
                ),
                const YBox(4),
                Text(
                  '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: order.payment?.amount ?? 0)}',
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
          // Container(
          //   padding: EdgeInsets.symmetric(
          //     vertical: Sizer.height(6),
          //     horizontal: Sizer.width(8),
          //   ),
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(Sizer.radius(8)),
          //     border: Border.all(
          //       color: AppColors.blackBD,
          //     ),
          //   ),
          //   child: Center(
          //     child: Text(
          //       '${order.products?.length ?? 0} item(s)',
          //       style: AppTypography.text12,
          //     ),
          //   ),
          // )
          const XBox(16),
          Icon(
            Icons.arrow_forward_ios,
            size: Sizer.height(16),
            color: AppColors.black70,
          )
        ],
      ),
    );
  }
}
