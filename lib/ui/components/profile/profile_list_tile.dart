import 'package:bottle_king_mobile/core/core.dart';

class ProfileListTile extends StatelessWidget {
  const ProfileListTile({
    super.key,
    required this.title,
    required this.leadIconPath,
    this.onTap,
  });

  final String title;
  final String leadIconPath;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          SvgPicture.asset(leadIconPath),
          const XBox(12),
          Expanded(
            child: Text(
              title,
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: Sizer.height(16),
            color: AppColors.black70,
          ),
        ],
      ),
    );
  }
}
