import 'package:bottle_king_mobile/core/core.dart';

class CheckoutAmountTile extends StatelessWidget {
  const CheckoutAmountTile({
    super.key,
    required this.leftText,
    required this.rightText,
    this.color,
  });

  final String leftText;
  final String rightText;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          leftText,
          style: AppTypography.text14.copyWith(
            fontWeight: FontWeight.w600,
            color: color ?? AppColors.black70,
          ),
        ),
        const Spacer(),
        Text(
          rightText,
          style: AppTypography.text14.copyWith(
            fontWeight: FontWeight.w600,
            color: color ?? AppColors.black70,
          ),
        ),
      ],
    );
  }
}
