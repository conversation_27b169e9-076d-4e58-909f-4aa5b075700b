import 'package:bottle_king_mobile/core/core.dart';

class CartIncrement extends StatelessWidget {
  const CartIncrement({
    super.key,
    required this.value,
    this.vPadding,
    this.onCrement,
    this.onDecrement,
    this.height,
    this.width,
    required this.controller,
  });

  final int value;
  final VoidCallback? onCrement;
  final VoidCallback? onDecrement;
  final double? vPadding;
  final double? height;
  final double? width;
  final TextEditingController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.height(height ?? 30),
      width: Sizer.width(width ?? 100),
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(4),
        vertical: Sizer.height(vPadding ?? 0),
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.blackBD,
        ),
      ),
      child: Row(
        children: [
          InkWell(
              onTap: onDecrement,
              child: Icon(
                Icons.remove,
                size: Sizer.height(20),
                color: AppColors.black70,
              )),
          const XBox(6),
          Expanded(
            child: TextField(
              controller: controller,
              textAlign: TextAlign.center,
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w500,
              ),
              decoration: const InputDecoration(
                isCollapsed: true,
                isDense: true,
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          const XBox(6),
          InkWell(
            onTap: onCrement,
            child: Icon(
              Icons.add,
              size: Sizer.height(20),
              color: AppColors.black70,
            ),
          ),
        ],
      ),
    );
  }
}
