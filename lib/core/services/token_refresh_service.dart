import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';
import 'package:dio/dio.dart';

/// Service for handling token refresh operations
/// This service is independent of the main API service to avoid circular dependencies
class TokenRefreshService {
  static bool _isRefreshing = false;
  static final List<Completer<String?>> _refreshCompleters = [];

  /// Refreshes the access token using the stored refresh token
  /// Returns the new access token if successful, null otherwise
  static Future<String?> refreshAccessToken() async {
    try {
      // If already refreshing, wait for the current refresh to complete
      if (_isRefreshing) {
        final completer = Completer<String?>();
        _refreshCompleters.add(completer);
        return await completer.future;
      }

      _isRefreshing = true;

      final refreshToken = await StorageService.getRefreshToken();
      if (refreshToken == null) {
        printty("No refresh token available", logName: "Token Refresh Service");
        await _handleRefreshFailure();
        return null;
      }

      printty("Attempting to refresh access token",
          logName: "Token Refresh Service");

      // Create a separate Dio instance for refresh requests to avoid interceptor loops
      final dio = Dio(BaseOptions(
        baseUrl: AppConfig.baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Accept': 'application/json',
          'bk': 'cGro2jdDtL4CFNSJ1UWzUeZUVjJkydwP',
        },
      ));

      final formData = FormData.fromMap({"refreshToken": refreshToken});

      final response = await dio.post(
        "/auth/refresh-token",
        data: formData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final newAccessToken = response.data["data"]?["accessToken"];
        final newRefreshToken = response.data["data"]?["refreshToken"];

        if (newAccessToken != null) {
          await StorageService.storeAccessToken(newAccessToken);
          if (newRefreshToken != null) {
            await StorageService.storeRefreshToken(newRefreshToken);
          }

          printty("Token refresh successful", logName: "Token Refresh Service");

          // Complete all pending requests with the new token
          for (final completer in _refreshCompleters) {
            if (!completer.isCompleted) {
              completer.complete(newAccessToken);
            }
          }
          _refreshCompleters.clear();

          return newAccessToken;
        }
      }

      printty("Token refresh failed: ${response.statusMessage}",
          logName: "Token Refresh Service");
      await _handleRefreshFailure();
      return null;
    } catch (e) {
      printty("Token refresh error: $e", logName: "Token Refresh Service");
      await _handleRefreshFailure();
      return null;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Handles refresh token failure by clearing tokens and notifying pending requests
  static Future<void> _handleRefreshFailure() async {
    printty("Handling refresh failure", logName: "Token Refresh Service");

    // Complete all pending requests with null
    for (final completer in _refreshCompleters) {
      if (!completer.isCompleted) {
        completer.complete(null);
      }
    }
    _refreshCompleters.clear();

    // Clear stored tokens
    await StorageService.removeAccessToken();
    await StorageService.removeRefreshToken();
  }

  /// Checks if a token refresh is currently in progress
  static bool get isRefreshing => _isRefreshing;

  /// Clears the refresh state (useful for logout)
  static void clearRefreshState() {
    _isRefreshing = false;
    for (final completer in _refreshCompleters) {
      if (!completer.isCompleted) {
        completer.complete(null);
      }
    }
    _refreshCompleters.clear();
  }
}
