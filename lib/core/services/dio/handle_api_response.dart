import 'package:bottle_king_mobile/core/core.dart';

void handleApiResponse({
  required ApiResponse response,
  String? successMsg,
  void Function()? onSuccess,
}) {
  if (response.success) {
    if (onSuccess != null) onSuccess();
    FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.success,
        message: successMsg ?? response.message ?? 'Operation successful');
  } else {
    FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: response.message ?? 'Something went wrong');
  }
}
