// payment_service.dart
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_paystack/flutter_paystack.dart';

class PaymentResult {
  final bool success;
  final String message;
  final String? reference;
  final Map<String, dynamic>? data;

  PaymentResult({
    required this.success,
    required this.message,
    this.reference,
    this.data,
  });
}

class PaystackService {
  // Singleton pattern
  static final PaystackService _instance = PaystackService._internal();
  factory PaystackService() => _instance;
  PaystackService._internal();

  // Paystack plugin instance
  final PaystackPlugin _plugin = PaystackPlugin();
  bool _isInitialized = false;

  // Configuration
  final String _publicKey = dotenv.env['PAYSTACK_TEST_KEY'] ?? "";
  final String _currency = 'NGN';

  /// Initialize the Paystack service with your configuration
  void initialize() {
    _plugin.initialize(publicKey: _publicKey);
    _isInitialized = true;
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Generate a random reference for the transaction

  /// Initiate payment with Paystack
  Future<PaymentResult> makePayment({
    required BuildContext context,
    required double amount,
    required String email,
    required String reference,
    Map<String, String>? metadata,
  }) async {
    if (!_isInitialized) {
      return PaymentResult(
        success: false,
        message: 'Paystack service not initialized',
      );
    }

    try {
      // Convert amount to the smallest currency unit (e.g., kobo for NGN)
      final int amountInSmallestUnit = (amount * 100).toInt();

      // Create the charge
      final Charge charge = Charge()
        ..amount = amountInSmallestUnit
        ..reference = reference
        ..email = email
        ..currency = _currency;

      // Add metadata if provided
      if (metadata != null && metadata.isNotEmpty) {
        metadata.forEach((key, value) {
          charge.putCustomField(key, value);
        });
      }

      // Present the checkout UI
      final CheckoutResponse response = await _plugin.checkout(
        context,
        method: CheckoutMethod.card,
        fullscreen: true,
        charge: charge,
      );

      if (response.status && response.reference != null) {
        // Verify on server if a backend URL was provided
        return PaymentResult(
          success: true,
          message: 'Payment was successful and verified',
          reference: response.reference,
        );
      } else {
        // Payment failed or was cancelled
        return PaymentResult(
          success: false,
          message: 'Payment failed or was cancelled',
          reference: response.reference,
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        message: 'An error occurred: ${e.toString()}',
      );
    }
  }

  /// Verify the transaction on the server
  // Future<PaymentResult> _verifyTransaction(String reference) async {
  //   try {
  //     final response = await http.get(
  //       Uri.parse('$_backendUrl?reference=$reference'),
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //     );

  //     final Map<String, dynamic> data = json.decode(response.body);

  //     final bool isSuccessful = data['status'] == true &&
  //         data['data'] != null &&
  //         data['data']['status'] == 'success';

  //     return PaymentResult(
  //       success: isSuccessful,
  //       message:
  //           isSuccessful ? 'Verification successful' : 'Verification failed',
  //       reference: reference,
  //       data: data,
  //     );
  //   } catch (e) {
  //     return PaymentResult(
  //       success: false,
  //       message: 'Verification Error: ${e.toString()}',
  //       reference: reference,
  //     );
  //   }
  // }

  /// Directly verify a transaction without processing a payment
  /// Useful for when you need to check transaction status after app restart
  // Future<PaymentResult> verifyTransaction(String reference) async {
  //   if (!_isInitialized) {
  //     return PaymentResult(
  //       success: false,
  //       message: 'Paystack service not initialized',
  //     );
  //   }

  //   return await _verifyTransaction(reference);
  // }
}
