import 'package:bottle_king_mobile/lib.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

class LocationService {
  /// Determines the current position of the device.
  /// When the location services are not enabled or permissions
  /// are denied the `Future` will return an error.
  static Future<Position> getCurrentPosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    try {
      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return Future.error('Location services are disabled.');
      }

      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return Future.error('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return Future.error(
            'Location permissions are permanently denied, we cannot request permissions.');
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      return Future.error('Failed to get current position: $e');
    }
  }

  /// Gets the user's current location including latitude, longitude, and full address
  /// Returns a map with keys: 'latitude', 'longitude', and 'address'
  static Future<Map<String, dynamic>> getUserLocationDetails(
      {required WidgetRef ref}) async {
    try {
      // Get current position
      final Position position = await getCurrentPosition();

      // Get address from the coordinates
      final List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // Build a formatted address from the placemark
      final Placemark place = placemarks.first;
      final String address = '${place.street}, '
          '${place.subLocality}, '
          '${place.locality}, '
          '${place.postalCode}, '
          '${place.country}';

      ref.read(addressVm.notifier).setCurrentAddress(AddressArg(
            fullAddress: address,
            lat: position.latitude,
            lng: position.longitude,
          ));

      final Map<String, dynamic> result = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'address': address,
        'placemark':
            place, // Including the raw placemark for more detailed info if needed
      };

      printty("Result: $result");

      return result;
    } catch (e) {
      // Return error information
      return {
        'error': e.toString(),
      };
    }
  }
}
