class AddressArg {
  final String? title;
  final String? type; // Home, work, other, pickup
  final String? street;
  final String? city;
  final String? state;
  final String? fullAddress;
  final double? lat;
  final double? lng;
  final bool? isDefault;

  AddressArg({
    this.title,
    this.type,
    this.street,
    this.city,
    this.state,
    this.fullAddress,
    this.lat,
    this.lng,
    this.isDefault,
  });

  factory AddressArg.fromJson(Map<String, dynamic> json) => AddressArg(
        title: json["title"],
        type: json["type"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        fullAddress: json["fullAddress"],
        lat: json["lat"]?.toDouble(),
        lng: json["lng"]?.toDouble(),
        isDefault: json["isDefault"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "type": type,
        "street": street,
        "city": city,
        "state": state,
        "fullAddress": fullAddress,
        "lat": lat,
        "lng": lng,
        "isDefault": isDefault,
      };
}
