import 'dart:convert';

CartModel cartModelFromJson(String str) => CartModel.fromJson(json.decode(str));

String cartModelToJson(CartModel data) => json.encode(data.toJson());

class CartModel {
  final String? id;
  final String? sessionId;
  final int? total;
  final bool? isWishList;
  final List<CartItem>? items;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;

  CartModel({
    this.id,
    this.sessionId,
    this.total,
    this.isWishList,
    this.items,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) => CartModel(
        id: json["_id"],
        sessionId: json["sessionId"],
        total: json["total"],
        isWishList: json["isWishList"],
        items: json["items"] == null
            ? []
            : List<CartItem>.from(
                json["items"]!.map((x) => CartItem.fromJson(x))),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "sessionId": sessionId,
        "total": total,
        "isWishList": isWishList,
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}

class CartItem {
  final String? id;
  final String? productId;
  final String? variationId;
  final String? productName;
  final String? variationName;
  final int? quantity;
  final int? price;
  final String? image;
  final int? cartonPrice;
  final String? cartonSize;

  CartItem({
    this.id,
    this.productId,
    this.variationId,
    this.productName,
    this.variationName,
    this.quantity,
    this.price,
    this.image,
    this.cartonPrice,
    this.cartonSize,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
        id: json["_id"],
        productId: json["productId"],
        variationId: json["variationId"],
        productName: json["productName"],
        variationName: json["variationName"],
        quantity: json["quantity"],
        price: json["price"],
        image: json["image"],
        cartonPrice: json["cartonPrice"],
        cartonSize: json["cartonSize"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "productId": productId,
        "variationId": variationId,
        "productName": productName,
        "variationName": variationName,
        "quantity": quantity,
        "price": price,
        "image": image,
        "cartonPrice": cartonPrice,
        "cartonSize": cartonSize,
      };
}
