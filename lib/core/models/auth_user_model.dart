// To parse this JSON data, do
//
//     final authUser = authUserFromJson(jsonString);

import 'package:bottle_king_mobile/core/core.dart';

AuthUserModel authUserModelFromJson(String str) =>
    AuthUserModel.fromJson(json.decode(str));

String authUserModelToJson(AuthUserModel data) => json.encode(data.toJson());

class AuthUserModel {
  final Freebies? freebies;
  final String? id;
  final bool? admin;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final bool? verified;
  final bool? blocked;
  final int? points;
  final int? friendsOrdered;
  final DateTime? lastLogin;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final bool? isFirstTimeUser;
  final String? code;
  final int? v;
  final String? authType;
  final String? addressLine;
  final List<AddressModel>? addresses;
  final String? defaultAddress;
  final Role? role;
  final String? roleType;

  AuthUserModel({
    this.freebies,
    this.id,
    this.admin,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.verified,
    this.blocked,
    this.points,
    this.friendsOrdered,
    this.lastLogin,
    this.createdAt,
    this.lastUpdated,
    this.isFirstTimeUser,
    this.code,
    this.v,
    this.authType,
    this.addressLine,
    this.addresses,
    this.defaultAddress,
    this.role,
    this.roleType,
  });

  factory AuthUserModel.fromJson(Map<String, dynamic> json) => AuthUserModel(
        freebies: json["freebies"] == null
            ? null
            : Freebies.fromJson(json["freebies"]),
        id: json["_id"],
        admin: json["admin"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        email: json["email"],
        phone: json["phone"],
        verified: json["verified"],
        blocked: json["blocked"],
        points: json["points"],
        friendsOrdered: json["friends_ordered"],
        lastLogin: json["last_login"] == null
            ? null
            : DateTime.parse(json["last_login"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        isFirstTimeUser: json["isFirstTimeUser"],
        code: json["code"],
        v: json["__v"],
        authType: json["authType"],
        addressLine: json["address_line"],
        addresses: json["addresses"] == null
            ? []
            : List<AddressModel>.from(
                json["addresses"]!.map((x) => AddressModel.fromJson(x))),
        defaultAddress: json["defaultAddress"],
        role: json["role"] == null ? null : Role.fromJson(json["role"]),
        roleType: json["roleType"],
      );

  Map<String, dynamic> toJson() => {
        "freebies": freebies?.toJson(),
        "_id": id,
        "admin": admin,
        "firstname": firstname,
        "lastname": lastname,
        "email": email,
        "phone": phone,
        "verified": verified,
        "blocked": blocked,
        "points": points,
        "friends_ordered": friendsOrdered,
        "last_login": lastLogin?.toIso8601String(),
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "isFirstTimeUser": isFirstTimeUser,
        "code": code,
        "__v": v,
        "authType": authType,
        "address_line": addressLine,
        "addresses": addresses == null
            ? []
            : List<dynamic>.from(addresses!.map((x) => x.toJson())),
        "defaultAddress": defaultAddress,
        "role": role?.toJson(),
        "roleType": roleType,
      };
}

class Freebies {
  final bool? mobile;

  Freebies({
    this.mobile,
  });

  factory Freebies.fromJson(Map<String, dynamic> json) => Freebies(
        mobile: json["mobile"],
      );

  Map<String, dynamic> toJson() => {
        "mobile": mobile,
      };
}

class Role {
  final Permissions? permissions;
  final String? id;
  final String? name;
  final String? description;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final int? v;

  Role({
    this.permissions,
    this.id,
    this.name,
    this.description,
    this.createdAt,
    this.lastUpdated,
    this.v,
  });

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        permissions: json["permissions"] == null
            ? null
            : Permissions.fromJson(json["permissions"]),
        id: json["_id"],
        name: json["name"],
        description: json["description"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "permissions": permissions?.toJson(),
        "_id": id,
        "name": name,
        "description": description,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "__v": v,
      };
}

class Permissions {
  final Cart? dashboard;
  final Coupons? customers;
  final Cart? cart;
  final Coupons? orders;
  final Coupons? events;
  final Coupons? notifications;
  final Coupons? products;
  final Coupons? coupons;
  final Coupons? discounts;
  final Coupons? userManagement;

  Permissions({
    this.dashboard,
    this.customers,
    this.cart,
    this.orders,
    this.events,
    this.notifications,
    this.products,
    this.coupons,
    this.discounts,
    this.userManagement,
  });

  factory Permissions.fromJson(Map<String, dynamic> json) => Permissions(
        dashboard:
            json["dashboard"] == null ? null : Cart.fromJson(json["dashboard"]),
        customers: json["customers"] == null
            ? null
            : Coupons.fromJson(json["customers"]),
        cart: json["cart"] == null ? null : Cart.fromJson(json["cart"]),
        orders:
            json["orders"] == null ? null : Coupons.fromJson(json["orders"]),
        events:
            json["events"] == null ? null : Coupons.fromJson(json["events"]),
        notifications: json["notifications"] == null
            ? null
            : Coupons.fromJson(json["notifications"]),
        products: json["products"] == null
            ? null
            : Coupons.fromJson(json["products"]),
        coupons:
            json["coupons"] == null ? null : Coupons.fromJson(json["coupons"]),
        discounts: json["discounts"] == null
            ? null
            : Coupons.fromJson(json["discounts"]),
        userManagement: json["userManagement"] == null
            ? null
            : Coupons.fromJson(json["userManagement"]),
      );

  Map<String, dynamic> toJson() => {
        "dashboard": dashboard?.toJson(),
        "customers": customers?.toJson(),
        "cart": cart?.toJson(),
        "orders": orders?.toJson(),
        "events": events?.toJson(),
        "notifications": notifications?.toJson(),
        "products": products?.toJson(),
        "coupons": coupons?.toJson(),
        "discounts": discounts?.toJson(),
        "userManagement": userManagement?.toJson(),
      };
}

class Cart {
  final bool? view;
  final bool? edit;

  Cart({
    this.view,
    this.edit,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => Cart(
        view: json["view"],
        edit: json["edit"],
      );

  Map<String, dynamic> toJson() => {
        "view": view,
        "edit": edit,
      };
}

class Coupons {
  final bool? view;
  final bool? edit;
  final bool? create;
  final bool? delete;

  Coupons({
    this.view,
    this.edit,
    this.create,
    this.delete,
  });

  factory Coupons.fromJson(Map<String, dynamic> json) => Coupons(
        view: json["view"],
        edit: json["edit"],
        create: json["create"],
        delete: json["delete"],
      );

  Map<String, dynamic> toJson() => {
        "view": view,
        "edit": edit,
        "create": create,
        "delete": delete,
      };
}

class AuthArg {
  final String? username;
  final String? password;
  final String? authType;
  final String? provider;
  final String? providerId;
  final String? platform;

  AuthArg({
    this.username,
    this.password,
    this.authType,
    this.provider,
    this.providerId,
    this.platform = "mobile",
  });

  Map<String, dynamic> toMap() {
    return {
      'username': username,
      'password': password,
      'authType': authType,
      'provider': provider,
      'providerId': providerId,
      'platform': platform,
    };
  }
}
