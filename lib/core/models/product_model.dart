import 'dart:convert';

List<ProductModel> productFromJson(String str) => List<ProductModel>.from(
    json.decode(str).map((x) => ProductModel.fromJson(x)));

String productToJson(List<ProductModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ProductModel {
  final String? id;
  final String? name;
  final dynamic desc;
  final dynamic category;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final List<Variation>? variations;
  final dynamic status;

  ProductModel({
    this.id,
    this.name,
    this.desc,
    this.category,
    this.createdAt,
    this.lastUpdated,
    this.variations,
    this.status,
  });

  ProductModel copyWith({
    String? id,
    String? name,
    dynamic desc,
    dynamic category,
    DateTime? createdAt,
    DateTime? lastUpdated,
    List<Variation>? variations,
    dynamic status,
  }) =>
      ProductModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        category: category ?? this.category,
        createdAt: createdAt ?? this.createdAt,
        lastUpdated: lastUpdated ?? this.lastUpdated,
        variations: variations ?? this.variations,
        status: status ?? this.status,
      );

  factory ProductModel.fromJson(Map<String, dynamic> json) => ProductModel(
        id: json["_id"],
        name: json["name"],
        desc: json["desc"],
        category: json["category"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        variations: json["variations"] == null
            ? []
            : List<Variation>.from(
                json["variations"]!.map((x) => Variation.fromJson(x))),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "desc": desc,
        "category": category,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "variations": variations == null
            ? []
            : List<dynamic>.from(variations!.map((x) => x.toJson())),
        "status": status,
      };
}

List<Variation> variationFromJson(String str) =>
    List<Variation>.from(json.decode(str).map((x) => Variation.fromJson(x)));

class Variation {
  final String? name;
  final String? productId; // for extracting all variations from products
  final String? productName; // for extracting all variations from products
  final String? volume;
  final String? sku;
  final int? unitPrice;
  final int? cartonPrice;
  final String? desc;
  final String? cartonSize;
  final String? category;
  final String? image;
  final String? barcode;
  final String? status;
  final bool? isBestSeller;
  final bool? isRecommended;
  final bool? isNewArrival;
  final dynamic discount;
  final Metadata? metadata;
  final String? slug;
  final String? id;
  final int? stockQuantity;

  Variation({
    this.name,
    this.productId,
    this.productName,
    this.volume,
    this.sku,
    this.unitPrice,
    this.cartonPrice,
    this.desc,
    this.cartonSize,
    this.category,
    this.image,
    this.barcode,
    this.status,
    this.isBestSeller,
    this.isRecommended,
    this.isNewArrival,
    this.discount,
    this.metadata,
    this.slug,
    this.id,
    this.stockQuantity,
  });

  Variation copyWith({
    String? name,
    String? volume,
    String? sku,
    int? unitPrice,
    int? cartonPrice,
    String? desc,
    String? cartonSize,
    String? category,
    String? image,
    String? barcode,
    String? status,
    bool? isBestSeller,
    bool? isRecommended,
    bool? isNewArrival,
    dynamic discount,
    Metadata? metadata,
    String? slug,
    String? id,
    int? stockQuantity,
  }) =>
      Variation(
        name: name ?? this.name,
        volume: volume ?? this.volume,
        sku: sku ?? this.sku,
        unitPrice: unitPrice ?? this.unitPrice,
        cartonPrice: cartonPrice ?? this.cartonPrice,
        desc: desc ?? this.desc,
        cartonSize: cartonSize ?? this.cartonSize,
        category: category ?? this.category,
        image: image ?? this.image,
        barcode: barcode ?? this.barcode,
        status: status ?? this.status,
        isBestSeller: isBestSeller ?? this.isBestSeller,
        isRecommended: isRecommended ?? this.isRecommended,
        isNewArrival: isNewArrival ?? this.isNewArrival,
        discount: discount ?? this.discount,
        metadata: metadata ?? this.metadata,
        slug: slug ?? this.slug,
        id: id ?? this.id,
        stockQuantity: stockQuantity ?? this.stockQuantity,
      );

  factory Variation.fromJson(Map<String, dynamic> json) => Variation(
        name: json["name"],
        volume: json["volume"],
        sku: json["sku"],
        unitPrice: json["unitPrice"],
        cartonPrice: json["cartonPrice"],
        desc: json["desc"],
        cartonSize: json["cartonSize"],
        category: json["category"],
        image: json["image"],
        barcode: json["barcode"],
        status: json["status"],
        isBestSeller: json["isBestSeller"],
        isRecommended: json["isRecommended"],
        isNewArrival: json["isNewArrival"],
        discount: json["discount"],
        metadata: json["metadata"] == null
            ? null
            : Metadata.fromJson(json["metadata"]),
        slug: json["slug"],
        id: json["_id"],
        stockQuantity: json["stockQuantity"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "volume": volume,
        "sku": sku,
        "unitPrice": unitPrice,
        "cartonPrice": cartonPrice,
        "desc": desc,
        "cartonSize": cartonSize,
        "category": category,
        "image": image,
        "barcode": barcode,
        "status": status,
        "isBestSeller": isBestSeller,
        "isRecommended": isRecommended,
        "isNewArrival": isNewArrival,
        "discount": discount,
        "metadata": metadata?.toJson(),
        "slug": slug,
        "_id": id,
        "stockQuantity": stockQuantity,
      };
}

class Metadata {
  final String? quickBooksUnitId;
  final String? quickBooksCaseId;

  Metadata({
    this.quickBooksUnitId,
    this.quickBooksCaseId,
  });

  Metadata copyWith({
    String? quickBooksUnitId,
    String? quickBooksCaseId,
  }) =>
      Metadata(
        quickBooksUnitId: quickBooksUnitId ?? this.quickBooksUnitId,
        quickBooksCaseId: quickBooksCaseId ?? this.quickBooksCaseId,
      );

  factory Metadata.fromJson(Map<String, dynamic> json) => Metadata(
        quickBooksUnitId: json["quickBooksUnitId"],
        quickBooksCaseId: json["quickBooksCaseId"],
      );

  Map<String, dynamic> toJson() => {
        "quickBooksUnitId": quickBooksUnitId,
        "quickBooksCaseId": quickBooksCaseId,
      };
}
