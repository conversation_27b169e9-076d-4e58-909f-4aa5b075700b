String productCategoryHelper(String category) {
  // Convert to lowercase for case-insensitive matching
  String lowerCategory = category.toLowerCase();

  // Beer categories
  if (lowerCategory.contains("beer")) {
    return "Beer";
  }

  // Champagne categories
  else if (lowerCategory.contains("champagne") ||
      lowerCategory.contains("champange") ||
      lowerCategory.contains("sparkling") ||
      lowerCategory.contains("bubbly")) {
    return "Champagne";
  }

  // Spirits categories
  else if (lowerCategory.contains("bitters") ||
      lowerCategory.contains("brandy") ||
      lowerCategory.contains("calvados")) {
    return "Brandy";
  } else if (lowerCategory.contains("cognac")) {
    return "Cognac";
  } else if (lowerCategory.contains("gin")) {
    return "Gin";
  } else if (lowerCategory.contains("liquer")) {
    return "Liqueur"; // Fixed spelling
  } else if (lowerCategory.contains("rum")) {
    return "Rum";
  } else if (lowerCategory.contains("tequila")) {
    return "Tequila";
  } else if (lowerCategory.contains("vodka")) {
    return "Vodka";
  } else if (lowerCategory.contains("whisky") ||
      lowerCategory.contains("whiskey")) {
    return "Whisky";
  }

  // Wine category
  else if (lowerCategory.contains("wine")) {
    return "Wine";
  }

  // Other categories
  else if (lowerCategory.contains("mixers") ||
      lowerCategory.contains("water")) {
    return "Mixers & Water";
  } else if (lowerCategory == "beverages") {
    return "Beverages";
  } else if (lowerCategory == "extra") {
    return "Extra";
  }

  // Default case - return original with first letter capitalized
  else {
    return category.isNotEmpty
        ? category[0].toUpperCase() + category.substring(1)
        : category;
  }
}
