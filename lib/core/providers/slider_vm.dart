import 'package:bottle_king_mobile/core/core.dart';

class SliderVm extends BaseVm {
  List<SliderModel> _sliders = [];
  List<SliderModel> get sliders => _sliders;

  Future<ApiResponse> getSliders() async {
    return await performApiCall(
      url: "/image/sliders/all",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _sliders = sliderModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final sliderVmodel = ChangeNotifierProvider((ref) => SliderVm());
