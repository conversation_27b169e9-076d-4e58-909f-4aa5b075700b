import 'package:bottle_king_mobile/core/core.dart';

const String getCartState = "getCartState";
const String getWishlistState = "getWishlistState";
const String removeState = "removeState";
const String reorderState = "reorderState";
const String rewardGenerateCouponState = "rewardGenerateCouponState";
const String validateCouponState = "validateCouponState";

class CartVm extends BaseVm {
  CartModel? _cartModel;
  CartModel? get cartModel => _cartModel;
  CartModel? _wishList;
  List<CartItem> get wishList => _wishList?.items ?? [];

  Future<ApiResponse> addToCartOrWishlist({
    required String productId,
    required String variationId,
    String quantity = "1",
    bool isWishList = false,
  }) async {
    printty("addToCartOrWishlist productId: $productId");
    return await performApiCall(
      url: "/cart/add",
      method: apiService.postWithAuth,
      isFormData: false,
      body: {
        "productId": productId,
        "variationId": variationId,
        "quantity": quantity,
        "isWishList": isWishList
      },
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateCartOrWishlist({
    required String itemId,
    int quantity = 1,
    bool isWishList = false,
  }) async {
    printty("addToCartOrWishlist itemId: $itemId");
    return await performApiCall(
      url: "/cart/update",
      method: apiService.putWithAuth,
      isFormData: true,
      body: {
        "itemId": itemId,
        "quantity": quantity,
        "isWishList": isWishList,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCart({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=false",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getCartState : "",
      onSuccess: (data) {
        _cartModel = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getWishlist({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=true",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getWishlistState : "",
      onSuccess: (data) {
        _wishList = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> removeCartOrWishlist({
    required String itemId,
    bool isWishList = false,
  }) async {
    printty("removeCartOrWishlist productId: $itemId");
    return await performApiCall(
      url: "/cart/remove/$itemId?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> clearCartOrWishlist({
    bool isWishList = false,
  }) async {
    return await performApiCall(
      url: "/cart/clear?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<Coupon>> rewardGenerateCoupon(
    String customerId,
  ) async {
    return await performApiCall<Coupon>(
      url: "/reward/generate-coupon",
      method: apiService.postWithAuth,
      busyObjectName: rewardGenerateCouponState,
      body: {"customerId": customerId},
      onSuccess: (data) {
        // getCart(showLoader: false);
        return ApiResponse<Coupon>(
          success: true,
          data: Coupon.fromJson(data["data"]["coupon"]),
        );
      },
    );
  }

  Future<ApiResponse> validateCoupon(
    String code,
  ) async {
    return await performApiCall(
      url: "/coupon/validate?code=$code",
      method: apiService.getWithAuth,
      busyObjectName: validateCouponState,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: data["data"]?["value"],
        );
      },
    );
  }

  Future<ApiResponse> reorder({
    required String orderId,
  }) async {
    return await performApiCall(
      url: "/order/reorder/$orderId",
      method: apiService.postWithAuth,
      busyObjectName: reorderState,
      onSuccess: (data) {
        getCart(showLoader: false);
        return apiResponse;
      },
    );
  }
}

final cartVm = ChangeNotifierProvider((ref) {
  return CartVm();
});
